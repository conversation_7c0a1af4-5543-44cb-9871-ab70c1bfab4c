import { useState, useEffect } from 'react'

interface BotConfig {
  accountType: 'Demo' | 'Live'
  number: string
  search: string
}

interface BotStatus {
  isRunning: boolean
  isPaused: boolean
  currentUrl: string
  status: string
}

const BotInterface: React.FC = () => {
  const [isStarted, setIsStarted] = useState(false)
  const [botConfig, setBotConfig] = useState<BotConfig>({
    accountType: 'Demo',
    number: '1',
    search: 'picture of a cat'
  })
  const [botStatus, setBotStatus] = useState<BotStatus>({
    isRunning: false,
    isPaused: false,
    currentUrl: '',
    status: 'Ready'
  })
  const [domElements, setDomElements] = useState<any[]>([])

  useEffect(() => {
    // Listen for bot status updates from main process
    const removeBotStatus = window.api.onBotStatusUpdate?.((status: BotStatus) => {
      setBotStatus(status)
    })

    const removeDomElements = window.api.onDomElementsUpdate?.((elements: any[]) => {
      setDomElements(elements)
    })

    return () => {
      removeBotStatus?.()
      removeDomElements?.()
    }
  }, [])

  const handleBegin = async () => {
    try {
      setIsStarted(true)

      // Resize window for bot interface
      await window.api.resizeWindow?.(1200, 800)

      setBotStatus((prev) => ({ ...prev, status: 'Initializing browser...' }))

      const result = await window.api.initializeBot?.()
      if (result?.success) {
        setBotStatus((prev) => ({ ...prev, status: 'Browser initialized' }))

        // Navigate to Google
        await window.api.navigateToUrl?.('https://www.google.com')
        setBotStatus((prev) => ({
          ...prev,
          currentUrl: 'https://www.google.com',
          status: 'Navigated to Google'
        }))

        // Query DOM elements
        await window.api.queryDomElements?.()
      }
    } catch (error) {
      console.error('Error initializing bot:', error)
      setBotStatus((prev) => ({ ...prev, status: 'Error initializing bot' }))
    }
  }

  const handleStartBot = async () => {
    try {
      setBotStatus((prev) => ({ ...prev, isRunning: true, status: 'Bot started' }))
      await window.api.startBot?.(botConfig)
    } catch (error) {
      console.error('Error starting bot:', error)
      setBotStatus((prev) => ({ ...prev, status: 'Error starting bot' }))
    }
  }

  const handleStopBot = async () => {
    try {
      setBotStatus((prev) => ({
        ...prev,
        isRunning: false,
        isPaused: false,
        status: 'Bot stopped'
      }))
      await window.api.stopBot?.()
    } catch (error) {
      console.error('Error stopping bot:', error)
    }
  }

  const handlePauseBot = async () => {
    try {
      const newPausedState = !botStatus.isPaused
      setBotStatus((prev) => ({
        ...prev,
        isPaused: newPausedState,
        status: newPausedState ? 'Bot paused' : 'Bot resumed'
      }))
      await window.api.pauseBot?.(newPausedState)
    } catch (error) {
      console.error('Error pausing bot:', error)
    }
  }

  const handleConfigChange = (field: keyof BotConfig, value: string) => {
    setBotConfig((prev) => ({ ...prev, [field]: value }))
  }

  if (!isStarted) {
    return (
      <div className="bot-interface">
        <div className="bot-welcome">
          <h2>🤖 PocketBot</h2>
          <p>Welcome to PocketBot - Your automated browser assistant</p>
          <button onClick={handleBegin} className="btn-begin">
            Begin
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bot-interface expanded">
      <div className="bot-header">
        <h2>🤖 PocketBot Control Panel</h2>
        <div className="bot-status">
          <span className={`status-indicator ${botStatus.isRunning ? 'running' : 'stopped'}`}>
            {botStatus.isRunning ? '🟢' : '🔴'}
          </span>
          <span className="status-text">{botStatus.status}</span>
        </div>
      </div>

      <div className="bot-content">
        <div className="bot-config">
          <h3>Configuration</h3>

          <div className="form-group">
            <label>Account Type:</label>
            <div className="radio-group">
              <label className="radio-label">
                <input
                  type="radio"
                  name="accountType"
                  value="Demo"
                  checked={botConfig.accountType === 'Demo'}
                  onChange={(e) =>
                    handleConfigChange('accountType', e.target.value as 'Demo' | 'Live')
                  }
                />
                Demo
              </label>
              <label className="radio-label">
                <input
                  type="radio"
                  name="accountType"
                  value="Live"
                  checked={botConfig.accountType === 'Live'}
                  onChange={(e) =>
                    handleConfigChange('accountType', e.target.value as 'Demo' | 'Live')
                  }
                />
                Live
              </label>
            </div>
          </div>

          <div className="form-group">
            <label>Number:</label>
            <select
              value={botConfig.number}
              onChange={(e) => handleConfigChange('number', e.target.value)}
              className="form-select"
            >
              {[1, 2, 3, 4, 5].map((num) => (
                <option key={num} value={num.toString()}>
                  {num}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label>Search:</label>
            <input
              type="text"
              value={botConfig.search}
              onChange={(e) => handleConfigChange('search', e.target.value)}
              placeholder="picture of a cat"
              className="form-input"
            />
          </div>
        </div>

        <div className="bot-controls">
          <h3>Controls</h3>
          <div className="control-buttons">
            <button
              onClick={handleStartBot}
              disabled={botStatus.isRunning}
              className="btn-control btn-start"
            >
              Start Bot
            </button>
            <button
              onClick={handlePauseBot}
              disabled={!botStatus.isRunning}
              className="btn-control btn-pause"
            >
              {botStatus.isPaused ? 'Resume' : 'Pause'}
            </button>
            <button
              onClick={handleStopBot}
              disabled={!botStatus.isRunning}
              className="btn-control btn-stop"
            >
              Stop
            </button>
          </div>
        </div>

        <div className="bot-info">
          <h3>Browser Info</h3>
          <p>
            <strong>Current URL:</strong> {botStatus.currentUrl || 'Not navigated'}
          </p>
          <p>
            <strong>DOM Elements Found:</strong> {domElements.length}
          </p>

          {domElements.length > 0 && (
            <div className="dom-elements">
              <h4>Detected Elements:</h4>
              <div className="elements-list">
                {domElements.slice(0, 10).map((element, index) => (
                  <div key={index} className="element-item">
                    <span className="element-tag">{element.tagName}</span>
                    <span className="element-text">{element.text?.substring(0, 50)}...</span>
                  </div>
                ))}
                {domElements.length > 10 && (
                  <p className="more-elements">... and {domElements.length - 10} more elements</p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default BotInterface
